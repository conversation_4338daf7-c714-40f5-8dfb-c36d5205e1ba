using System.Data.Common;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities; // For ApplicationUser
using ProScoring.Infrastructure.Authorization; // For AuthTypes
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Services; // For GuidishIdGenerationUtilService
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.Authorization.Repositories
{
    /// <summary>
    /// Unit tests for the <see cref="OverridePermissionRepository"/> class.
    /// </summary>
    public class OverridePermissionRepositoryTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly DbConnection _connection;
        private readonly DbContextOptions<ApplicationDbContext> _dbContextOptions;
        private readonly ILogger<OverridePermissionRepository> _mockLogger;
        private ApplicationDbContext _dbContext = null!;
        private OverridePermissionRepository _repository = null!;

        /// <summary>
        /// Initializes a new instance of the <see cref="OverridePermissionRepositoryTests"/> class.
        /// </summary>
        public OverridePermissionRepositoryTests(ITestOutputHelper output)
        {
            _output = output;
            _connection = new SqliteConnection("Filename=:memory:");
            _connection.Open();

            _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>().UseSqlite(_connection).Options;

            _mockLogger = Substitute.For<ILogger<OverridePermissionRepository>>();
            RecreateDbContextAndRepository();
        }

        private void RecreateDbContextAndRepository()
        {
            _dbContext?.Dispose();

            var datetimeProvider = new FixedDateTimeOffsetProvider(2023, 1, 1, 12, 0, 0);
            var guidishLogger = SafeTestOutputHelperLoggerCreator.BuildSafeLoggerFor<GuidishIdGenerationUtilService>(
                _output
            );
            var guidishService = new GuidishIdGenerationUtilService(datetimeProvider, guidishLogger);

            var customIdGeneratorLogger = SafeTestOutputHelperLoggerCreator.BuildSafeLoggerFor<CustomIdValueGenerator>(
                _output
            );
            var customIdGenerator = new CustomIdValueGenerator(guidishService, customIdGeneratorLogger);

            var authStateProvider =
                Substitute.For<Microsoft.AspNetCore.Components.Authorization.AuthenticationStateProvider>();
            _dbContext = new ApplicationDbContext(
                _dbContextOptions,
                authStateProvider, // For AuthenticationStateProvider
                customIdGenerator, // For IValueGenerator
                Substitute.For<ILogger<ApplicationDbContext>>(),
                datetimeProvider // For IDateTimeOffsetProvider
            );
            _dbContext.Database.EnsureCreated();
            _repository = new OverridePermissionRepository(_dbContext, _mockLogger);
        }

        private async Task SeedData(params OverridePermission[] overridePermissions)
        {
            await _dbContext.OverridePermissions.AddRangeAsync(overridePermissions);
            await _dbContext.SaveChangesAsync();
            foreach (var entry in _dbContext.ChangeTracker.Entries()) // Detach to ensure fresh reads
            {
                entry.State = EntityState.Detached;
            }
        }

        // Helper to seed users if needed for FK constraints, adapt as necessary
        private async Task SeedUsers(params ApplicationUser[] users)
        {
            await _dbContext.Users.AddRangeAsync(users);
            await _dbContext.SaveChangesAsync();
            foreach (var entry in _dbContext.ChangeTracker.Entries<ApplicationUser>())
            {
                entry.State = EntityState.Detached;
            }
        }

        /// <summary>
        /// Disposes of the database connection after all tests in the class have run.
        /// </summary>
        public void Dispose()
        {
            _dbContext?.Dispose();
            _connection?.Dispose();
            GC.SuppressFinalize(this);
        }

        // --- Test methods will be added below ---

        [Fact]
        public async Task AddAsync_ShouldAddOverridePermissionToDatabase()
        {
            // Arrange
            // Seed a user for FK constraint
            var testUser = new ApplicationUser { Id = "user1", UserName = "testuser1" };
            await SeedUsers(testUser);
            RecreateDbContextAndRepository(); // Recreate after seeding user to ensure FKs can be resolved if context is fresh for repo

            var newOverride = new OverridePermission
            {
                UserId = "user1",
                TargetId = "target1",
                ActionName = AuthTypes.Actions.VIEW,
                IsAllowed = true,
                Reason = "Test reason",
                ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
                CreatedByUserId = "user1", // Can be the same user or a different admin user
            };

            // Act
            await _repository.AddAsync(newOverride);
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            var retrievedOverride = await _dbContext.OverridePermissions.FindAsync(newOverride.Id);
            Assert.NotNull(retrievedOverride);
            Assert.Equal("user1", retrievedOverride.UserId);
            Assert.Equal("target1", retrievedOverride.TargetId);
            Assert.Equal(AuthTypes.Actions.VIEW, retrievedOverride.ActionName);
            Assert.True(retrievedOverride.IsAllowed);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnCorrectOverridePermission_WhenExists()
        {
            // Arrange
            var user = new ApplicationUser { Id = "userGetById", UserName = "usergetbyid" };
            await SeedUsers(user);
            RecreateDbContextAndRepository();

            var overrideToSeed = new OverridePermission
            {
                UserId = user.Id,
                TargetId = "targetX",
                ActionName = AuthTypes.Actions.EDIT,
                IsAllowed = false,
            };
            await SeedData(overrideToSeed);
            RecreateDbContextAndRepository(); // Ensure clean read

            // Act
            var result = await _repository.GetByIdAsync(overrideToSeed.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(overrideToSeed.Id, result.Id);
            Assert.Equal(user.Id, result.UserId);
            Assert.Equal("targetX", result.TargetId);
            Assert.Equal(AuthTypes.Actions.EDIT, result.ActionName);
            Assert.False(result.IsAllowed);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
        {
            // Arrange
            // No data seeded for this Guid
            RecreateDbContextAndRepository();
            var nonExistentId = Guid.NewGuid();

            // Act
            var result = await _repository.GetByIdAsync(nonExistentId);

            // Assert
            Assert.Null(result);
        }

        // Remove or comment out the placeholder test
        // [Fact]
        // public void PlaceholderTest_ToRemove()
        // {
        //     Assert.True(true);
        // }

        [Fact]
        public async Task GetByUserAsync_ShouldReturnOnlyOverridesForGivenUser()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "user1", UserName = "user1" };
            var user2 = new ApplicationUser { Id = "user2", UserName = "user2" };
            await SeedUsers(user1, user2);
            RecreateDbContextAndRepository();

            await SeedData(
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target2",
                    ActionName = AuthTypes.Actions.EDIT,
                    IsAllowed = false,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user2.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user2.Id,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (await _repository.GetByUserAsync(user1.Id)).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, item => Assert.Equal(user1.Id, item.UserId));
        }

        [Fact]
        public async Task GetByUserAndTargetAsync_ShouldReturnMatchingOverrides()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "user1", UserName = "user1" };
            var user2 = new ApplicationUser { Id = "user2", UserName = "user2" };
            await SeedUsers(user1, user2);
            RecreateDbContextAndRepository();

            await SeedData(
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.EDIT,
                    IsAllowed = false,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target2",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user2.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user2.Id,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (await _repository.GetByUserAndTargetAsync(user1.Id, "target1")).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            foreach (var item in result)
            {
                Assert.Equal(user1.Id, item.UserId);
                Assert.Equal("target1", item.TargetId);
            }
            // Verify we have both VIEW and EDIT actions
            Assert.Contains(result, item => item.ActionName == AuthTypes.Actions.VIEW);
            Assert.Contains(result, item => item.ActionName == AuthTypes.Actions.EDIT);
        }

        [Fact]
        public async Task GetByUserTargetAndActionAsync_ShouldReturnMatchingOverrides()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "user1", UserName = "user1" };
            await SeedUsers(user1);
            RecreateDbContextAndRepository();

            await SeedData(
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = false,
                    ExpiresAt = DateTimeOffset.UtcNow.AddHours(1),
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.EDIT,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target2",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (
                await _repository.GetByUserTargetAndActionAsync(user1.Id, "target1", AuthTypes.Actions.VIEW)
            ).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Both VIEW actions for user1, target1
            Assert.All(
                result,
                item =>
                {
                    Assert.Equal(user1.Id, item.UserId);
                    Assert.Equal("target1", item.TargetId);
                    Assert.Equal(AuthTypes.Actions.VIEW, item.ActionName);
                }
            );
        }

        [Fact]
        public async Task GetActiveByUserTargetAndActionAsync_ShouldReturnOnlyActiveOverrides()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "user1", UserName = "user1" };
            await SeedUsers(user1);
            RecreateDbContextAndRepository();

            var currentTime = DateTimeOffset.UtcNow;

            await SeedData(
                // Active: No expiry
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                // Active: Expires in future
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = false,
                    ExpiresAt = currentTime.AddHours(1),
                    CreatedByUserId = user1.Id,
                },
                // Expired
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    ExpiresAt = currentTime.AddHours(-1),
                    CreatedByUserId = user1.Id,
                },
                // Different action
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.EDIT,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                },
                // Different target
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target2",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    CreatedByUserId = user1.Id,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (
                await _repository.GetActiveByUserTargetAndActionAsync(
                    user1.Id,
                    "target1",
                    AuthTypes.Actions.VIEW,
                    currentTime
                )
            ).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // The one with no expiry and the one expiring in the future
            foreach (var op in result)
            {
                Assert.Equal(user1.Id, op.UserId);
                Assert.Equal("target1", op.TargetId);
                Assert.Equal(AuthTypes.Actions.VIEW, op.ActionName);
            }
        }

        [Fact]
        public async Task GetActiveByUserTargetAndActionAsync_ShouldReturnEmpty_WhenAllExpiredOrNotMatching()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "user1", UserName = "user1" };
            await SeedUsers(user1);
            RecreateDbContextAndRepository();

            var currentTime = DateTimeOffset.UtcNow;

            await SeedData(
                // Expired
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = true,
                    ExpiresAt = currentTime.AddHours(-1),
                    CreatedByUserId = user1.Id,
                },
                new OverridePermission
                {
                    UserId = user1.Id,
                    TargetId = "target1",
                    ActionName = AuthTypes.Actions.VIEW,
                    IsAllowed = false,
                    ExpiresAt = currentTime.AddMinutes(-5),
                    CreatedByUserId = user1.Id,
                }
            );
            RecreateDbContextAndRepository();

            // Act
            var result = (
                await _repository.GetActiveByUserTargetAndActionAsync(
                    user1.Id,
                    "target1",
                    AuthTypes.Actions.VIEW,
                    currentTime
                )
            ).ToList();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task UpdateAsync_ShouldModifyExistingOverridePermissionInDatabase()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "userUpd", UserName = "userUpd" };
            await SeedUsers(user1);
            RecreateDbContextAndRepository();

            var initialOverride = new OverridePermission
            {
                UserId = user1.Id,
                TargetId = "targetUpd",
                ActionName = AuthTypes.Actions.VIEW,
                IsAllowed = true,
                Reason = "Initial Reason",
                ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
                CreatedByUserId = user1.Id,
            };
            await SeedData(initialOverride);

            // Detach and re-fetch to ensure we're updating an entity that's tracked by the current context for update.
            RecreateDbContextAndRepository();
            var overrideToUpdate = await _dbContext.OverridePermissions.FindAsync(initialOverride.Id);
            Assert.NotNull(overrideToUpdate); // Ensure it was found

            // Modify properties
            overrideToUpdate.IsAllowed = false;
            overrideToUpdate.Reason = "Updated Reason";
            overrideToUpdate.ExpiresAt = DateTimeOffset.UtcNow.AddDays(5);

            // Act
            await _repository.UpdateAsync(overrideToUpdate); // Update is synchronous in EF Core until SaveChanges
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            RecreateDbContextAndRepository(); // Ensure fresh read
            var updatedOverride = await _dbContext.OverridePermissions.FindAsync(initialOverride.Id);
            Assert.NotNull(updatedOverride);
            Assert.False(updatedOverride.IsAllowed);
            Assert.Equal("Updated Reason", updatedOverride.Reason);
            Assert.NotNull(updatedOverride.ExpiresAt);
            Assert.True(updatedOverride.ExpiresAt.Value.Date == DateTimeOffset.UtcNow.AddDays(5).Date); // Compare date part as exact time might differ slightly
        }

        [Fact]
        public async Task DeleteByIdAsync_ShouldRemoveOverridePermissionAndReturnTrue_WhenExists()
        {
            // Arrange
            var user1 = new ApplicationUser { Id = "userDel", UserName = "userDel" };
            await SeedUsers(user1);
            RecreateDbContextAndRepository();

            var overrideToDelete = new OverridePermission
            {
                UserId = user1.Id,
                TargetId = "targetDel",
                ActionName = AuthTypes.Actions.DELETE,
                IsAllowed = true,
                CreatedByUserId = user1.Id,
            };
            await SeedData(overrideToDelete);
            RecreateDbContextAndRepository(); // Ensure clean context for repository operation

            // Act
            var result = await _repository.DeleteByIdAsync(overrideToDelete.Id);
            await _dbContext.SaveChangesAsync(); // Simulate Unit of Work

            // Assert
            Assert.True(result);
            RecreateDbContextAndRepository(); // Ensure fresh read
            var deleted = await _repository.GetByIdAsync(overrideToDelete.Id);
            Assert.Null(deleted);
        }

        [Fact]
        public async Task DeleteByIdAsync_ShouldReturnFalse_WhenNotExists()
        {
            // Arrange
            RecreateDbContextAndRepository();
            var nonExistentId = Guid.NewGuid();

            // Act
            var result = await _repository.DeleteByIdAsync(nonExistentId);
            // No SaveChangesAsync needed

            // Assert
            Assert.False(result);
        }
    }
}
