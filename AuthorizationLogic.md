# Authorization Logic in ProScoring

## 1. Purpose

This document outlines the authorization logic implemented within the ProScoring application. It details the components involved, the order of operations for permission checks, and the design decisions made. Its goal is to serve as a reference for developers working on or interacting with the authorization system.

## 2. Core Permission Entities and Concepts

The authorization system is built around several key concepts and entities:

*   **`AuthTypes.cs`**: Located in `ProScoring.Infrastructure/Authorization/`, this static class is the central source of truth for string constants defining recognizable authorization actions (e.g., `View`, `Edit`, `Admin` via `AuthTypes.Actions`) and core entity type identifiers (e.g., `ORGANIZING_AUTHORITY`, `REGATTA` via `AuthTypes.Entities`). The decision was made to use these string constants directly in the database and throughout the application logic, rather than converting them to enums for database storage, primarily for database readability and to simplify migrations. The special `AuthTypes.UNIVERSAL_TARGET` (`"*"`) is used to denote permissions that apply to all targets of a given type or globally.

*   **`AuthAction` Entity**: (`ProScoring.Infrastructure/Authorization/Entities/AuthAction.cs`)
    *   Represents a defined, performable action within the system (e.g., "View", "Edit").
    *   Stored in the database and seeded from `AuthTypes.Actions` and `AuthTypes.GOD`.

*   **`ActionHierarchy` Entity**: (`ProScoring.Infrastructure/Authorization/Entities/ActionHierarchy.cs`)
    *   Defines parent-child relationships between `AuthAction`s. For example, "Admin" might be a parent to "Edit", which is a parent to "View". This allows for hierarchical permission granting (e.g., granting "Admin" implicitly grants "Edit" and "View").

*   **`UserAuthAction` Entity**: (`ProScoring.Infrastructure/Authorization/Entities/UserAuthAction.cs`)
    *   Represents a direct grant of a specific `AuthAction` to a `User` on a particular `TargetId` (or the universal target `"*"`).
    *   This is the primary way users receive permissions.

*   **`OverridePermission` Entity**: (`ProScoring.Infrastructure/Authorization/Entities/OverridePermission.cs`)
    *   Provides a mechanism to explicitly grant (`IsAllowed = true`) or deny (`IsAllowed = false`) a specific `AuthAction` for a `User` on a particular `TargetId` (or `"*"`).
    *   Overrides take precedence over permissions derived from `UserAuthAction`s.
    *   Overrides can be temporary (using `ExpiresAt`).

## 3. Authorization Service Structure

*   **`IAuthorizationProvider` Interface**: (`ProScoring.Infrastructure/Authorization/IAuthorizationProvider.cs`)
    *   The primary interface for authorization checks within the application.
*   **`ProScoringAuthorizationService` Class**: (`ProScoring.Infrastructure/Authorization/ProScoringAuthorizationService.cs`)
    *   The concrete implementation of `IAuthorizationProvider`.
    *   Orchestrates the authorization logic.
    *   Dependencies:
        *   `IUserAuthActionRepository`: For accessing `UserAuthAction` data.
        *   `IOverridePermissionRepository`: For accessing `OverridePermission` data.
        *   `IApplicationDbContext`: Currently still used by the `ExpandActionsAsync` helper method to access `ActionHierarchy` and `AuthAction` data directly.
        *   `ILogger`: For logging.
        *   `ITargetHierarchyService`: For hierarchical target lookups.

## 4. Detailed Authorization Flow

The `IsAuthorizedAsync(ClaimsPrincipal actor, string targetId, string action)` method in `ProScoringAuthorizationService` determines if a user has permission to perform an action on a target. The logic follows a strict order of precedence:

1.  **HMFIC Check (Super Admin)**:
    *   If the `actor` (user) possesses the HMFIC (Head Mucky-Muck in Charge) claim (`AuthTypes.HMFIC`), access is immediately GRANTED. This is a super administrator override.

2.  **User Identification**:
    *   The system extracts the User ID from the `actor`'s claims (`ClaimTypes.NameIdentifier`).
    *   If no User ID is found, access is DENIED.

3.  **Hierarchical Target Check (Specific Target and its Parents)**:
    *   The system retrieves the hierarchy of parent targets for the given `targetId` using the `ITargetHierarchyService`. This service returns an ordered list, from immediate parent to the furthest ancestor.
    *   A list of targets to check is formed: the original `targetId` first, followed by its parents in order.
    *   The system iterates through this list (from most specific target to most general ancestor):
        *   For the current `currentTargetIdInHierarchy` being checked:
            *   **a. Override Permissions Check**:
                *   Active (non-expired) `OverridePermission`s for the `userId`, `currentTargetIdInHierarchy`, and `action` are retrieved.
                *   If any "Deny" override (`IsAllowed = false`) exists, access is immediately DENIED.
                *   If any "Allow" override (`IsAllowed = true`) exists (and no "Deny" was found for this target), access is immediately GRANTED.
            *   **b. UserAuthAction Check (Direct and Hierarchical Actions)**:
                *   If no decisive override was found for `currentTargetIdInHierarchy`:
                    *   `UserAuthAction`s for the `userId` on `currentTargetIdInHierarchy` are retrieved.
                    *   The `AuthAction`s associated with these grants are collected.
                    *   These granted actions are then expanded using the `ExpandActionsAsync` helper method. This method consults the `ActionHierarchy` definitions (e.g., if "Admin" is granted, it expands to include "Edit", "View", etc.).
                    *   If the originally `requestedAction` is present in this expanded list of granted actions, access is GRANTED.
        *   If access is GRANTED or DENIED decisively at any point in this iteration, the process stops, and the decision is returned.

4.  **Universal Target (`*`) Check (Fallback)**:
    *   If no permission was granted or denied after checking the entire specific target hierarchy (original `targetId` and all its parents):
        *   **a. Universal Override Permissions Check**:
            *   Active `OverridePermission`s for the `userId`, the universal target (`AuthTypes.UNIVERSAL_TARGET`), and `action` are retrieved.
            *   If any "Deny" universal override exists, access is DENIED.
            *   If any "Allow" universal override exists (and no "Deny"), access is GRANTED.
        *   **b. Universal UserAuthAction Check**:
            *   If no decisive universal override:
                *   `UserAuthAction`s for the `userId` on `AuthTypes.UNIVERSAL_TARGET` are retrieved.
                *   Associated `AuthAction`s are collected and expanded via `ExpandActionsAsync`.
                *   If the `requestedAction` is in this expanded list, access is GRANTED.

5.  **Default Deny**:
    *   If none of the above checks result in an explicit grant, access is DENIED.

This hierarchical approach ensures that permissions can be set at various levels (e.g., Organizing Authority, Regatta, Race) and are inherited, while specific overrides can fine-tune access at any level. Deny overrides always take precedence.
