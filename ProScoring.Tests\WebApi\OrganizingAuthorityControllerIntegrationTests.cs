using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using ProScoring.Blazor.Controllers;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.BusinessLogic.Services;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Authorization.Repositories;
using ProScoring.Infrastructure.Authorization.Repositories.Interfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.WebApi;

[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
// this causes these tests to be run in serial.
public class OrganizingAuthorityControllerIntegrationTests
{
    private const string TEST_FILE_DIRECTORY = "\\_Temp\\ProScoring.Tests_WebApi";

    private readonly ITestOutputHelper _output;
    private readonly IServiceProvider _serviceProvider;
    private readonly ServiceCollection _services;

    public OrganizingAuthorityControllerIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        _services = new ServiceCollection();

        // setup tho AuthenticationStateProvider. We will need this to return a user and id when appropriate.
        _services.AddScopedSafeLogger<AuthenticationStateProvider>(output);
        _services.AddScopedSafeLogger<ApplicationDbContext>(output);
        _services.AddScopedSafeLogger<CustomIdValueGenerator>(output);
        _services.AddScopedSafeLogger<GuidishIdGenerationUtilService>(output);
        _services.AddScopedSafeLogger<OrganizingAuthorityController>(output);
        _services.AddScopedSafeLogger<OrganizingAuthorityService>(output);
        _services.AddScopedSafeLogger<FileService>(output);
        _services.AddScopedSafeLogger<ProScoringAuthorizationService>(output);
        _services.AddScopedSafeLogger<UserAuthActionRepository>(output);
        _services.AddScopedSafeLogger<OverridePermissionRepository>(output);

        // Setup authentication
        var claims = new List<Claim> { new(ClaimTypes.NameIdentifier, "userid1233") };
        var identity = new ClaimsIdentity(claims, "TestAuthType");
        var user = new ClaimsPrincipal(identity);
        var mockAuthStateProvider = Substitute.For<AuthenticationStateProvider>();
        mockAuthStateProvider.GetAuthenticationStateAsync().Returns(new AuthenticationState(user));
        _services.AddScoped(_ => mockAuthStateProvider);

        var mockHttpContextAccessor = Substitute.For<IHttpContextAccessor>();
        _services.AddScoped(_ => mockHttpContextAccessor);

        var mockAuthService = Substitute.For<IAuthorizationService>();
        mockAuthService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));
        _services.AddScoped(_ => mockAuthService);

        _services.AddScoped<IIdGenerationUtilService>(_ =>
        {
            return StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(
                _serviceProvider!.GetService<GuidishIdGenerationUtilService>()
                    ?? throw new InvalidOperationException("GuidishIdGenerationUtilService not found.")
            );
        });
        _services.AddScoped<IDateTimeOffsetProvider>(_ => new DateTimeOffsetProviderCpuTime());
        _services.AddScoped<GuidishIdGenerationUtilService>();
        _services.AddScoped<IValueGenerator, CustomIdValueGenerator>();
        _services.AddScoped(_ =>
            Substitute.For<ServiceAuthorizationHelper>(
                mockAuthService,
                mockHttpContextAccessor,
                Substitute.For<ILogger<ServiceAuthorizationHelper>>()
            )
        );
        _services.AddDbContext<IApplicationDbContext, ApplicationDbContext>(
            options =>
            {
                // Use a unique database name for each test instance to avoid conflicts
                var dbName = $"TestDb_{Guid.NewGuid():N}";
                options.UseSqlite($"Data Source={dbName};Mode=Memory;Cache=Shared");
            },
            ServiceLifetime.Scoped
        );
        _services.AddScoped<IOrganizingAuthorityService, OrganizingAuthorityService>();
        _services.AddTransient(_ =>
        {
            var mockOptions = Substitute.For<IOptions<FileUploadOptions>>();
            mockOptions.Value.Returns(new FileUploadOptions { MaxSize = 1024 * 5 });
            return mockOptions;
        });
        _services.AddScoped<IFileService, FileService>();
        // for picking the file save directory we need IHostEnvironment
        _services.AddTransient(_ =>
        {
            var mockHostEnvironment = Substitute.For<IHostEnvironment>();
            mockHostEnvironment.ContentRootPath.Returns(TEST_FILE_DIRECTORY);
            return mockHostEnvironment;
        });
        // Configure settings
        var inMemorySettings = new Dictionary<string, string?> { { "ASPNETCORE_ENVIRONMENT", "Development" } };
        _services.AddScoped<IConfiguration>(_ =>
        {
            return new ConfigurationBuilder().AddInMemoryCollection(inMemorySettings).Build();
        });
        _services.AddScoped(_ => new FileUploadOptions() { MaxSize = 1024 * 5 });

        // Register Authorization Repositories
        _services.AddScoped<IUserAuthActionRepository, UserAuthActionRepository>();
        _services.AddScoped<IOverridePermissionRepository, OverridePermissionRepository>();

        // Add mock ITargetHierarchyService
        var mockTargetHierarchyService = Substitute.For<ITargetHierarchyService>();
        mockTargetHierarchyService.GetTargetHierarchyAsync(Arg.Any<string>()).Returns(Task.FromResult(Enumerable.Empty<string>()));
        _services.AddScoped(_ => mockTargetHierarchyService);

        // Mock the authorization service to prevent duplicate UserAuthAction creation
        var mockProScoringAuthService = Substitute.For<IProScoringAuthorizationService>();
        mockProScoringAuthService.CreateUserAuthActionAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(Task.FromResult(new UserAuthAction()));
        _services.AddScoped(_ => mockProScoringAuthService);

        //// item under test
        _services.AddScoped<OrganizingAuthorityController>();

        _serviceProvider = _services.BuildServiceProvider();
        // make sure that the db was created
        var dbContext = _serviceProvider.GetService<ApplicationDbContext>();
        dbContext.Should().NotBeNull();
        dbContext!.Database.OpenConnection();
        dbContext!.Database.EnsureCreated();

        // Seed required users for foreign key constraints
        SeedRequiredUsers(dbContext);
    }

    private static void SeedRequiredUsers(ApplicationDbContext dbContext)
    {
        // Check if users already exist
        var existingUserIds = dbContext.Users.Select(u => u.Id).ToHashSet();
        var usersToAdd = new List<ApplicationUser>();

        // Add the null user if it doesn't exist
        if (!existingUserIds.Contains(ApplicationDbContext.NULL_USER_ID))
        {
            var nullUser = new ApplicationUser
            {
                Id = ApplicationDbContext.NULL_USER_ID,
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>",
                EmailConfirmed = true,
                PasswordHash = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                SecurityStamp = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                ConcurrencyStamp = "00000000-0000-0000-0000-000000000000",
                PhoneNumberConfirmed = false,
                TwoFactorEnabled = false,
                LockoutEnabled = true,
                AccessFailedCount = 0,
                CreatedById = ApplicationDbContext.NULL_USER_ID,
                UpdatedById = ApplicationDbContext.NULL_USER_ID,
                CreatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero),
                UpdatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero)
            };
            usersToAdd.Add(nullUser);
        }

        // Add the test user if it doesn't exist
        if (!existingUserIds.Contains("userid1233"))
        {
            var testUser = new ApplicationUser
            {
                Id = "userid1233",
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>",
                EmailConfirmed = true,
                PasswordHash = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                SecurityStamp = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                ConcurrencyStamp = "00000000-0000-0000-0000-000000000000",
                PhoneNumberConfirmed = false,
                TwoFactorEnabled = false,
                LockoutEnabled = true,
                AccessFailedCount = 0,
                CreatedById = ApplicationDbContext.NULL_USER_ID,
                UpdatedById = ApplicationDbContext.NULL_USER_ID,
                CreatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero),
                UpdatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero)
            };
            usersToAdd.Add(testUser);
        }

        if (usersToAdd.Count > 0)
        {
            dbContext.Users.AddRange(usersToAdd);
            dbContext.SaveChanges();
        }
    }

    [Fact]
    public async Task Create_ReturnsBadRequest_WhenDtoIsNull()
    {
        // Arrange
        var controllerUnderTest =
            _serviceProvider.GetService<OrganizingAuthorityController>()
            ?? throw new InvalidOperationException("OrganizingAuthorityController not found.");

        // Act
        var result = await controllerUnderTest!.Create(null);

        // Assert
        result.Should().BeOfType<BadRequestResult>();
    }

    [Fact]
    public async Task Create_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto
        {
            Name = "Test Authority",
            BurgeeDataUri = "data:image/png;base64,AAESIg==",
            BurgeeFileName = "burgee.png",
        };

        var controllerUnderTest =
            _serviceProvider.GetService<OrganizingAuthorityController>()
            ?? throw new InvalidOperationException("OrganizingAuthorityController not found.");

        // Act
        var result = await controllerUnderTest.Create(dto);

        // Assert
        var createdAtActionResult = result.Should().BeOfType<CreatedAtActionResult>().Subject;
        createdAtActionResult.ActionName.Should().Be(nameof(controllerUnderTest.GetById));
    }
    /*
            [Fact]
            public async Task Delete_ReturnsNoContent_WhenDeleteIsSuccessful()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Test Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);
                mockOrganizingAuthorityService.Setup(s => s.DeleteAsync("1")).Returns(Task.CompletedTask);

                // Act
                var result = await controllerUnderTest.Delete("1");

                // Assert
                result.Should().BeOfType<NoContentResult>();
            }

            [Fact]
            public async Task Delete_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                mockOrganizingAuthorityService
                    .Setup(s => s.GetByIdAsync(It.IsAny<string>()))
                    .ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.Delete("1");

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }

            [Fact]
            public async Task GetAll_ReturnsOkResult_WithListOfAuthorities()
            {
                // Arrange
                var authorities = new List<OrganizingAuthority>
                {
                    new OrganizingAuthority { Id = "1", Name = "Test Authority" },
                };
                mockOrganizingAuthorityService.Setup(s => s.GetAllAsync()).ReturnsAsync(authorities);

                // Act
                var result = await controllerUnderTest.GetAll();

                // Assert
                var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
                var returnValue = okResult.Value.Should().BeOfType<List<OrganizingAuthority>>().Subject;
                returnValue.Count.Should().Be(1);
            }

            [Fact]
            public async Task GetById_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                mockOrganizingAuthorityService
                    .Setup(s => s.GetByIdAsync(It.IsAny<string>()))
                    .ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.GetById("1");

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }

            [Fact]
            public async Task GetById_ReturnsOkResult_WithAuthority()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Test Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);

                // Act
                var result = await controllerUnderTest.GetById("1");

                // Assert
                var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
                var returnValue = okResult.Value.Should().BeOfType<OrganizingAuthority>().Subject;
                returnValue.Id.Should().Be("1");
            }

            [Fact]
            public async Task Update_ReturnsBadRequest_WhenIdDoesNotMatch()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };

                // Act
                var result = await controllerUnderTest.Update("2", authority);

                // Assert
                result.Should().BeOfType<BadRequestResult>();
            }

            [Fact]
            public async Task Update_ReturnsNoContent_WhenUpdateIsSuccessful()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);
                mockOrganizingAuthorityService.Setup(s => s.UpdateAsync(authority)).Returns(Task.CompletedTask);

                // Act
                var result = await controllerUnderTest.Update("1", authority);

                // Assert
                result.Should().BeOfType<NoContentResult>();
            }

            [Fact]
            public async Task Update_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.Update("1", authority);

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }
        */
}
